package sitereportworker

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"gorm.io/gorm"
)

type (
	SiteReportWorkerDomainItf interface {
		CreateWorkerWithTx(ctx context.Context, tx *gorm.DB, param CreateWorkerParam) (int64, error)
		UpdateWorkerWithTx(ctx context.Context, tx *gorm.DB, param UpdateWorkerParam) error
		DeleteWorkerWithTx(ctx context.Context, tx *gorm.DB, param DeleteWorkerParam) error
		GetBySiteReportID(ctx context.Context, siteReportID int64) ([]SiteReportWorker, error)
		GetWorkerReportList(ctx context.Context, param GetWorkerReportListParam) ([]WorkerReportListItem, error)
		BulkUpdateWorkerPaymentStatusWithTx(ctx context.Context, tx *gorm.DB, param BulkUpdateWorkerPaymentStatusParam) error
		GetWorkerPaymentStatementList(ctx context.Context, param GetWorkerPaymentStatementListParam) ([]WorkerPaymentStatementListItem, error)
		GetByIDs(ctx context.Context, ids []int64) ([]SiteReportWorker, error)
	}

	SiteReportWorkerResourceItf interface {
		createWorkerWithTx(ctx context.Context, tx *gorm.DB, param CreateWorkerParam) (int64, error)
		updateWorkerWithTx(ctx context.Context, tx *gorm.DB, param UpdateWorkerParam) error
		deleteWorkerWithTx(ctx context.Context, tx *gorm.DB, param DeleteWorkerParam) error
		getBySiteReportID(ctx context.Context, siteReportID int64) ([]SiteReportWorker, error)
		getWorkerReportList(ctx context.Context, param GetWorkerReportListParam) ([]WorkerReportListItem, error)
		bulkUpdateWorkerPaymentStatusWithTx(ctx context.Context, tx *gorm.DB, param BulkUpdateWorkerPaymentStatusParam) error
		getWorkerPaymentStatementList(ctx context.Context, param GetWorkerPaymentStatementListParam) ([]WorkerPaymentStatementListItem, error)
		getByIDs(ctx context.Context, ids []int64) ([]SiteReportWorker, error)
	}
)

// CreateWorkerWithTx creates a new site report worker record within a transaction.
func (d *SiteReportWorkerDomain) CreateWorkerWithTx(ctx context.Context, tx *gorm.DB, param CreateWorkerParam) (int64, error) {
	id, err := d.resource.createWorkerWithTx(ctx, tx, param)
	if err != nil {
		return 0, log.LogError(err, nil)
	}
	return id, nil
}

// UpdateWorker updates an existing site report worker record.
func (d *SiteReportWorkerDomain) UpdateWorkerWithTx(ctx context.Context, tx *gorm.DB, param UpdateWorkerParam) error {
	err := d.resource.updateWorkerWithTx(ctx, tx, param)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}

// DeleteWorker deletes a site report worker record.
func (d *SiteReportWorkerDomain) DeleteWorkerWithTx(ctx context.Context, tx *gorm.DB, param DeleteWorkerParam) error {
	err := d.resource.deleteWorkerWithTx(ctx, tx, param)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}

// GetBySiteReportID retrieves site report worker records by site report ID.
func (d *SiteReportWorkerDomain) GetBySiteReportID(ctx context.Context, siteReportID int64) ([]SiteReportWorker, error) {
	workers, err := d.resource.getBySiteReportID(ctx, siteReportID)
	if err != nil {
		return []SiteReportWorker{}, log.LogError(err, nil)
	}
	return workers, nil
}

// GetWorkerReportList retrieves site report worker records with site report and user data filtered by date range.
func (d *SiteReportWorkerDomain) GetWorkerReportList(ctx context.Context, param GetWorkerReportListParam) ([]WorkerReportListItem, error) {
	workers, err := d.resource.getWorkerReportList(ctx, param)
	if err != nil {
		return []WorkerReportListItem{}, log.LogError(err, nil)
	}
	return workers, nil
}

// BulkUpdateWorkerPaymentStatusWithTx updates the payment status and issued_date for multiple site report worker records.
func (d *SiteReportWorkerDomain) BulkUpdateWorkerPaymentStatusWithTx(ctx context.Context, tx *gorm.DB, param BulkUpdateWorkerPaymentStatusParam) error {
	err := d.resource.bulkUpdateWorkerPaymentStatusWithTx(ctx, tx, param)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}

// GetWorkerPaymentStatementList retrieves worker payment statement records with site report and user data filtered by date range.
func (d *SiteReportWorkerDomain) GetWorkerPaymentStatementList(ctx context.Context, param GetWorkerPaymentStatementListParam) ([]WorkerPaymentStatementListItem, error) {
	workers, err := d.resource.getWorkerPaymentStatementList(ctx, param)
	if err != nil {
		return []WorkerPaymentStatementListItem{}, log.LogError(err, nil)
	}
	return workers, nil
}

// GetByIDs retrieves site report worker records by IDs.
func (d *SiteReportWorkerDomain) GetByIDs(ctx context.Context, ids []int64) ([]SiteReportWorker, error) {
	workers, err := d.resource.getByIDs(ctx, ids)
	if err != nil {
		return []SiteReportWorker{}, log.LogError(err, nil)
	}
	return workers, nil
}
