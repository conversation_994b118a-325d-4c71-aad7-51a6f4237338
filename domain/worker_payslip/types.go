package worker_payslip

import (
	"time"

	"github.com/lib/pq"
)

type WorkerPayslip struct {
	ID                  int64         `gorm:"column:id;primary_key"`
	SiteReportWorkerIDs pq.Int64Array `gorm:"column:site_report_worker_ids;type:_int8"`
	WorkerName          string        `gorm:"column:worker_name"`
	IssuedDate          time.Time     `gorm:"column:issued_date"`
	DebtPayment         float64       `gorm:"column:debt_payment"`
	TaxableSalary       float64       `gorm:"column:taxable_salary"`
	TaxFreeSalary       float64       `gorm:"column:tax_free_salary"`
	IncomeTaxAmount     float64       `gorm:"column:income_tax_amount"`
	TotalPayment        float64       `gorm:"column:total_payment"`
}

type CreateParam struct {
	SiteReportWorkerIDs []int64   `json:"site_report_worker_ids"`
	WorkerName          string    `json:"worker_name"`
	IssuedDate          time.Time `json:"issued_date"`
	DebtPayment         float64   `json:"debt_payment"`
	TaxableSalary       float64   `json:"taxable_salary"`
	TaxFreeSalary       float64   `json:"tax_free_salary"`
	IncomeTaxAmount     float64   `json:"income_tax_amount"`
	TotalPayment        float64   `json:"total_payment"`
}
