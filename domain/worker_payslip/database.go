package worker_payslip

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/lib/pq"
	"gorm.io/gorm"
)

// bulkCreateWithTx inserts multiple worker payslip records into the database.
func (rsc WorkerPayslipResource) bulkCreateWithTx(ctx context.Context, tx *gorm.DB, param []CreateParam) ([]WorkerPayslip, error) {
	db := tx.WithContext(ctx)
	var payslips []WorkerPayslip

	for _, v := range param {
		payslip := WorkerPayslip{
			SiteReportWorkerIDs: pq.Int64Array(v.SiteReportWorkerIDs),
			WorkerName:          v.WorkerName,
			IssuedDate:          v.IssuedDate,
			DebtPayment:         v.DebtPayment,
			TaxableSalary:       v.TaxableSalary,
			TaxFreeSalary:       v.TaxFreeSalary,
			IncomeTaxAmount:     v.IncomeTaxAmount,
			TotalPayment:        v.TotalPayment,
		}

		payslips = append(payslips, payslip)
	}

	err := db.Create(&payslips).Error
	if err != nil {
		return []WorkerPayslip{}, log.LogError(err, nil)
	}

	return payslips, nil
}
